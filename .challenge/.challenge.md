
# Cozero - Coding Challenge

Welcome to the Cozero coding challenge 🚀

Our idea is to offer you a fun and practical challenge so that we can later use it as a conversation starter to go over your thought process.

## Task

Our team of climate experts needs to know as quickly as possible about new developments in the area of carbon accounting, in particular when related to new regulations and carbon accounting standards coming up. We would like to ask you to write a small Python application that uses AI to effectively search the web for news in that area, summarize them and provide a concise daily overview of the development to our climate experts.

The task should cover the following:

1. Write a Python application that automatically generates daily summaries of the news related to carbon accounting regulation and carbon accounting standards.
2. Make sure the output is easy to consume and act upon for our climate experts.
3. You can use any AI model that you think is suitable for the task.

Please provide us with the following output when completing the challenge:

1. Your code either by just sending it to us or <NAME_EMAIL> to your Github repository or similar.
2. An example output of your code.
3. Note that we don’t require you to deploy the model anywhere. However, please make sure you set up the application in a way that it’s easy for anyone to run it.

## Notes

1. We are not looking for a UI here but rather for the backend generating the summaries. In a real world scenario, such summaries might then be served to climate experts automatically (e.g. via a Slack channel) but you are not required to include that for the challenge.
2. We mainly want to see two skill sets:
	a. You are comfortable writing production-ready Python code, so a focus on delivering clean code is a plus.
	b. You are able to build software solutions around AI models, and you are able to select suitable AI models for the problems at hand. We are mostly interested in the conceptual solution though, and won’t judge the quality of the output directly, so you can use a free model even though you think a paid one might give better results.
3. There’s no time limit imposed but we encourage you to work on the challenge for a maximum of 4 hours and hand in whatever you have got by then. We won’t reject your application just based on not having finished the full scope. Ideally, you would have a running state that demonstrates your coding and AI implementation skills, even if it delivers only a smaller part of the scope outlined above.
Thank you for taking the time to work on our coding challenge. We are looking forward to
discussing your submission with you.