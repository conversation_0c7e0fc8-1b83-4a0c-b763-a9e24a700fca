"""
Scheduled Task API Application
A properly architected Python application with scheduled tasks and REST endpoints.
"""

import logging
import threading
import time
from datetime import datetime, timezone
from typing import List, Optional, Dict, Any
from dataclasses import dataclass
from enum import Enum
import schedule
import uvicorn
from fastapi import FastAPI, HTTPException, BackgroundTasks
from pydantic import BaseModel, Field
import json

# ============================================================================
# CONFIGURATION
# ============================================================================

@dataclass
class AppConfig:
    """Application configuration"""
    log_level: str = "INFO"
    api_host: str = "0.0.0.0"
    api_port: int = 8000
    schedule_interval_minutes: int = 1
    max_results_history: int = 100

config = AppConfig()

# ============================================================================
# LOGGING SETUP
# ============================================================================

def setup_logging():
    """Configure application logging"""
    logging.basicConfig(
        level=getattr(logging, config.log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('app.log')
        ]
    )
    return logging.getLogger(__name__)

logger = setup_logging()

# ============================================================================
# MODELS AND SCHEMAS
# ============================================================================

class TaskStatus(str, Enum):
    """Task execution status enumeration"""
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"

class TaskResult(BaseModel):
    """Pydantic model for task execution results"""
    id: str = Field(..., description="Unique task execution ID")
    task_name: str = Field(..., description="Name of the executed task")
    status: TaskStatus = Field(..., description="Task execution status")
    started_at: datetime = Field(..., description="Task start timestamp")
    completed_at: Optional[datetime] = Field(None, description="Task completion timestamp")
    duration_seconds: Optional[float] = Field(None, description="Task execution duration")
    result_data: Dict[str, Any] = Field(default_factory=dict, description="Task result data")
    error_message: Optional[str] = Field(None, description="Error message if task failed")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class TaskSummary(BaseModel):
    """Summary statistics for task executions"""
    total_executions: int = Field(..., description="Total number of task executions")
    successful_executions: int = Field(..., description="Number of successful executions")
    failed_executions: int = Field(..., description="Number of failed executions")
    average_duration_seconds: Optional[float] = Field(None, description="Average execution duration")
    last_execution: Optional[datetime] = Field(None, description="Timestamp of last execution")
    last_success: Optional[datetime] = Field(None, description="Timestamp of last successful execution")

class HealthResponse(BaseModel):
    """Health check response model"""
    status: str = Field(..., description="Application health status")
    timestamp: datetime = Field(..., description="Health check timestamp")
    scheduler_running: bool = Field(..., description="Whether scheduler is active")
    total_tasks: int = Field(..., description="Total number of executed tasks")

# ============================================================================
# TASK STORAGE AND MANAGEMENT
# ============================================================================

class TaskStorage:
    """In-memory storage for task results with thread safety"""
    
    def __init__(self, max_history: int = 100):
        self._results: List[TaskResult] = []
        self._lock = threading.Lock()
        self._max_history = max_history
        logger.info(f"TaskStorage initialized with max_history={max_history}")
    
    def add_result(self, result: TaskResult) -> None:
        """Add a task result to storage"""
        with self._lock:
            self._results.append(result)
            # Keep only the most recent results
            if len(self._results) > self._max_history:
                self._results = self._results[-self._max_history:]
            logger.debug(f"Added task result: {result.id} ({result.status})")
    
    def get_results(self, limit: Optional[int] = None, status_filter: Optional[TaskStatus] = None) -> List[TaskResult]:
        """Get task results with optional filtering"""
        with self._lock:
            results = self._results.copy()
            
            if status_filter:
                results = [r for r in results if r.status == status_filter]
            
            results.sort(key=lambda x: x.started_at, reverse=True)
            
            if limit:
                results = results[:limit]
                
            return results
    
    def get_summary(self) -> TaskSummary:
        """Generate task execution summary"""
        with self._lock:
            if not self._results:
                return TaskSummary(
                    total_executions=0,
                    successful_executions=0,
                    failed_executions=0
                )
            
            successful = [r for r in self._results if r.status == TaskStatus.SUCCESS]
            failed = [r for r in self._results if r.status == TaskStatus.FAILED]
            
            durations = [r.duration_seconds for r in self._results if r.duration_seconds is not None]
            avg_duration = sum(durations) / len(durations) if durations else None
            
            last_execution = max(self._results, key=lambda x: x.started_at).started_at
            last_success = max(successful, key=lambda x: x.started_at).started_at if successful else None
            
            return TaskSummary(
                total_executions=len(self._results),
                successful_executions=len(successful),
                failed_executions=len(failed),
                average_duration_seconds=avg_duration,
                last_execution=last_execution,
                last_success=last_success
            )

# Global task storage instance
task_storage = TaskStorage(max_history=config.max_results_history)

# ============================================================================
# SCHEDULED TASKS
# ============================================================================

class ScheduledTaskRunner:
    """Manages and executes scheduled tasks"""
    
    def __init__(self, storage: TaskStorage):
        self.storage = storage
        self.is_running = False
        self._stop_event = threading.Event()
        logger.info("ScheduledTaskRunner initialized")
    
    def example_data_processing_task(self) -> Dict[str, Any]:
        """Example task that processes some data"""
        import random
        
        # Simulate some data processing
        processed_items = random.randint(50, 200)
        processing_time = random.uniform(0.5, 3.0)
        time.sleep(processing_time)
        
        # Simulate occasional failures
        if random.random() < 0.1:  # 10% failure rate
            raise Exception("Simulated processing error")
        
        return {
            "processed_items": processed_items,
            "processing_time": round(processing_time, 2),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
    
    def execute_task(self, task_name: str, task_func) -> None:
        """Execute a single task and store the result"""
        task_id = f"{task_name}_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')}"
        start_time = datetime.now(timezone.utc)
        
        logger.info(f"Starting task execution: {task_id}")
        
        result = TaskResult(
            id=task_id,
            task_name=task_name,
            status=TaskStatus.RUNNING,
            started_at=start_time
        )
        
        try:
            # Execute the task
            task_data = task_func()
            end_time = datetime.now(timezone.utc)
            
            result.status = TaskStatus.SUCCESS
            result.completed_at = end_time
            result.duration_seconds = (end_time - start_time).total_seconds()
            result.result_data = task_data
            
            logger.info(f"Task completed successfully: {task_id} in {result.duration_seconds:.2f}s")
            
        except Exception as e:
            end_time = datetime.now(timezone.utc)
            result.status = TaskStatus.FAILED
            result.completed_at = end_time
            result.duration_seconds = (end_time - start_time).total_seconds()
            result.error_message = str(e)
            
            logger.error(f"Task failed: {task_id} - {e}")
        
        self.storage.add_result(result)
    
    def start_scheduler(self) -> None:
        """Start the task scheduler in a background thread"""
        if self.is_running:
            logger.warning("Scheduler is already running")
            return
        
        self.is_running = True
        
        # Schedule the task
        schedule.every(config.schedule_interval_minutes).minutes.do(
            self.execute_task, 
            "data_processing", 
            self.example_data_processing_task
        )
        
        def run_scheduler():
            logger.info(f"Starting scheduler with {config.schedule_interval_minutes} minute intervals")
            while not self._stop_event.is_set():
                schedule.run_pending()
                time.sleep(1)
            logger.info("Scheduler stopped")
        
        scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
        scheduler_thread.start()
        logger.info("Scheduler thread started")
    
    def stop_scheduler(self) -> None:
        """Stop the task scheduler"""
        if not self.is_running:
            return
        
        self.is_running = False
        self._stop_event.set()
        schedule.clear()
        logger.info("Scheduler stop requested")

# Global task runner instance
task_runner = ScheduledTaskRunner(task_storage)

# ============================================================================
# FASTAPI APPLICATION
# ============================================================================

app = FastAPI(
    title="Scheduled Task API",
    description="A REST API for monitoring scheduled task executions",
    version="1.0.0"
)

@app.on_event("startup")
async def startup_event():
    """Initialize the application on startup"""
    logger.info("Application starting up...")
    task_runner.start_scheduler()
    # Execute one task immediately for demonstration
    background_task = BackgroundTasks()
    background_task.add_task(
        task_runner.execute_task, 
        "initial_data_processing", 
        task_runner.example_data_processing_task
    )

@app.on_event("shutdown")
async def shutdown_event():
    """Clean up resources on shutdown"""
    logger.info("Application shutting down...")
    task_runner.stop_scheduler()

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    return HealthResponse(
        status="healthy",
        timestamp=datetime.now(timezone.utc),
        scheduler_running=task_runner.is_running,
        total_tasks=len(task_storage.get_results())
    )

@app.get("/results", response_model=List[TaskResult])
async def get_task_results(
    limit: Optional[int] = 50,
    status: Optional[TaskStatus] = None
):
    """Get task execution results with optional filtering"""
    if limit and limit <= 0:
        raise HTTPException(status_code=400, detail="Limit must be positive")
    
    results = task_storage.get_results(limit=limit, status_filter=status)
    logger.info(f"Retrieved {len(results)} task results (limit={limit}, status={status})")
    return results

@app.get("/results/{task_id}", response_model=TaskResult)
async def get_task_result(task_id: str):
    """Get a specific task result by ID"""
    results = task_storage.get_results()
    for result in results:
        if result.id == task_id:
            return result
    
    raise HTTPException(status_code=404, detail=f"Task result not found: {task_id}")

@app.get("/summary", response_model=TaskSummary)
async def get_task_summary():
    """Get task execution summary statistics"""
    summary = task_storage.get_summary()
    logger.info("Retrieved task execution summary")
    return summary

@app.post("/execute", response_model=dict)
async def manual_execute_task(background_tasks: BackgroundTasks):
    """Manually trigger a task execution"""
    background_tasks.add_task(
        task_runner.execute_task,
        "manual_data_processing",
        task_runner.example_data_processing_task
    )
    
    logger.info("Manual task execution triggered")
    return {"message": "Task execution started", "timestamp": datetime.now(timezone.utc)}

# ============================================================================
# MAIN APPLICATION ENTRY POINT
# ============================================================================

if __name__ == "__main__":
    logger.info(f"Starting application on {config.api_host}:{config.api_port}")
    uvicorn.run(
        "main:app",
        host=config.api_host,
        port=config.api_port,
        log_level=config.log_level.lower(),
        reload=False
    )