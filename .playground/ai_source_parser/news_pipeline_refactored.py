from __future__ import annotations

import os
import re
import sys
from dataclasses import dataclass
from datetime import datetime, timezone
from typing import Optional

import dotenv
from pydantic_ai import Agent
from pydantic_ai.models.openai import OpenAIChatModel
from pydantic_ai.providers.openrouter import OpenRouterProvider

# Local imports
CURRENT_DIR = os.path.dirname(__file__)
if CURRENT_DIR not in sys.path:
    sys.path.append(CURRENT_DIR)

from models import (
    Classification,
    Content,
    CorporateDisclosureDetails,
    CourtEnforcementDetails,
    EventDetails,
    FundingIncentiveDetails,
    GuidanceStandardDetails,
    ImportantDate,
    ItemType,
    LegislationDetails,
    MarketAuctionDetails,
    NewsItem,
    ProposedRuleDetails,
    ResearchReportDetails,
    RegulatoryUpdateDetails,
    Source,
    Category,
    Framework,
    Instrument,
    RegulatoryAction,
    ProposedStage,
    LegislationStage,
    JurisdictionLevel,
    Outcome,
)

dotenv.load_dotenv()


class ModelManager:
    """Singleton model manager to avoid creating multiple model instances."""
    
    _instance: Optional[OpenAIChatModel] = None
    
    @classmethod
    def get_model(cls) -> OpenAIChatModel:
        if cls._instance is None:
            api_key = os.getenv("OPENROUTER_API_KEY")
            if not api_key:
                raise RuntimeError("Missing OPENROUTER_API_KEY in environment")
            
            cls._instance = OpenAIChatModel(
                "openai/gpt-4.1-mini",
                provider=OpenRouterProvider(api_key=api_key),
            )
        return cls._instance


class PromptTemplates:
    """Centralized prompt templates."""
    
    @staticmethod
    def enum_values(enum_cls) -> str:
        return ", ".join([e.value for e in enum_cls])
    
    CLASSIFICATION = f"""You extract policy news classification with strict enums.
Return a single Classification with:
- category: one of: {enum_values.__func__(Category)}
- type: one of: {enum_values.__func__(ItemType)}
- jurisdictions: list of country/region strings or ISO-3166 codes (primary first)
- sectors: optional short list (e.g., power, transport, industry)

Rules: If unsure, pick 'Unknown'/'Unspecified' variants. Favor precision and brevity."""

    CONTENT = """Extract normalized content as a Content object: title, 2–4 sentence summary, 3–7 concise key_points, and original_text (cleaned).
- Title: short, news-style
- Summary: objective and specific
- Key points: bullets (facts, numbers, actions)
- original_text: cleaned article text; trim boilerplate, strip menus/ads; limit to ~8000 chars
If text lacks a clear title, derive one."""

    REGULATORY_UPDATE = f"""Extract RegulatoryUpdateDetails. Fields: regulator?, instrument?, action, effective_date?, important_dates[], scope_note?
instrument: one of [{enum_values.__func__(Instrument)}]
action: one of [{enum_values.__func__(RegulatoryAction)}]
Dates must be ISO8601 if present. If unknown, leave None.
For important_dates: extract ALL mentioned dates with descriptions (e.g., implementation deadlines, review dates, compliance dates)."""

    PROPOSED_RULE = f"""Extract ProposedRuleDetails. Fields: regulator?, stage, comment_deadline?, effective_date_estimate?, important_dates[], proposal_note?
stage: one of [{enum_values.__func__(ProposedStage)}]. Dates must be ISO8601 if present.
For important_dates: extract ALL mentioned dates with descriptions (e.g., consultation periods, hearing dates, decision deadlines)."""

    LEGISLATION = f"""Extract LegislationDetails. Fields: bill_or_act_name, stage, jurisdiction_level?, effective_date?, important_dates[], summary_note?
stage: one of [{enum_values.__func__(LegislationStage)}]; jurisdiction_level: one of [{enum_values.__func__(JurisdictionLevel)}]
Dates must be ISO8601 if present.
For important_dates: extract ALL mentioned dates with descriptions (e.g., committee votes, hearings, implementation dates)."""

    COURT_ENFORCEMENT = f"""Extract CourtEnforcementDetails. Fields: authority, outcome, decision_date?, important_dates[], penalties_note?, impacted_policy_note?
outcome: one of [{enum_values.__func__(Outcome)}]. Dates must be ISO8601 if present.
For important_dates: extract ALL mentioned dates with descriptions (e.g., hearing dates, appeal deadlines, investigation milestones, future determinations).
Example: If article mentions "preliminary determinations expected Oct 10" create ImportantDate with date=2025-10-10, description="Preliminary countervailing duty determination", is_estimate=true."""

    GUIDANCE_STANDARD = """Extract GuidanceStandardDetails. Fields: issuing_body, document_name, applicability_date?, key_changes[], scope_note?
Date must be ISO8601."""

    MARKET_AUCTION = """Extract MarketAuctionDetails. Fields: market, date, clearing_price?, volume_note?, price_trend_note?
Date must be ISO8601. If price mentioned, return Money with amount (float) and currency (e.g., USD, EUR)."""

    CORPORATE_DISCLOSURE = f"""Extract CorporateDisclosureDetails. Fields: company, framework?, disclosure_or_target_note, period_note?, assurance_note?
framework: one of [{enum_values.__func__(Framework)}]."""

    FUNDING_INCENTIVE = """Extract FundingIncentiveDetails. Fields: program_name, agency, incentive_type, budget_note?, application_deadline?
incentive_type: grant, tax_credit, rebate, loan, tender, unspecified. Date must be ISO8601."""

    EVENT = """Extract EventDetails. Fields: event_name, start_date, end_date?, location?, registration_url?
Dates must be ISO8601; registration_url if any."""

    RESEARCH_REPORT = """Extract ResearchReportDetails. Fields: publisher, report_title, release_date?, headline_findings[], report_url?
Date must be ISO8601; findings concise."""


class AgentFactory:
    """Factory for creating agents with shared model."""
    
    def __init__(self):
        self.model = ModelManager.get_model()
        self._agents = {}
    
    def get_classification_agent(self) -> Agent:
        if 'classification' not in self._agents:
            self._agents['classification'] = Agent(
                self.model,
                output_type=Classification,
                system_prompt=PromptTemplates.CLASSIFICATION,
            )
        return self._agents['classification']
    
    def get_content_agent(self) -> Agent:
        if 'content' not in self._agents:
            self._agents['content'] = Agent(
                self.model,
                output_type=Content,
                system_prompt=PromptTemplates.CONTENT,
            )
        return self._agents['content']
    
    def get_detail_agent(self, item_type: ItemType) -> Optional[Agent]:
        """Get the appropriate detail extraction agent for the given item type."""
        agent_configs = {
            ItemType.REGULATORY_UPDATE: (RegulatoryUpdateDetails, PromptTemplates.REGULATORY_UPDATE),
            ItemType.PROPOSED_RULE: (ProposedRuleDetails, PromptTemplates.PROPOSED_RULE),
            ItemType.LEGISLATION: (LegislationDetails, PromptTemplates.LEGISLATION),
            ItemType.COURT_ENFORCEMENT: (CourtEnforcementDetails, PromptTemplates.COURT_ENFORCEMENT),
            ItemType.GUIDANCE_STANDARD: (GuidanceStandardDetails, PromptTemplates.GUIDANCE_STANDARD),
            ItemType.MARKET_AUCTION: (MarketAuctionDetails, PromptTemplates.MARKET_AUCTION),
            ItemType.CORPORATE_DISCLOSURE: (CorporateDisclosureDetails, PromptTemplates.CORPORATE_DISCLOSURE),
            ItemType.FUNDING_INCENTIVE: (FundingIncentiveDetails, PromptTemplates.FUNDING_INCENTIVE),
            ItemType.EVENT: (EventDetails, PromptTemplates.EVENT),
            ItemType.RESEARCH_REPORT: (ResearchReportDetails, PromptTemplates.RESEARCH_REPORT),
        }
        
        if item_type not in agent_configs:
            return None
            
        cache_key = f'detail_{item_type.value}'
        if cache_key not in self._agents:
            output_type, prompt = agent_configs[item_type]
            self._agents[cache_key] = Agent(
                self.model,
                output_type=output_type,
                system_prompt=prompt,
            )
        
        return self._agents[cache_key]


@dataclass
class ExtractInput:
    url: str
    outlet: Optional[str]
    raw_content: str


class DateExtractor:
    """Utility for extracting dates from content."""
    
    @staticmethod
    def extract_published_at(text: str) -> Optional[datetime]:
        """Best-effort extraction of an ISO8601 datetime from raw content."""
        # Look for Published Time: 2025-08-29T21:05:00.129Z
        patterns = [
            r"Published\s*Time:\s*([0-9T:\-.+Z]+)",
            r"(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?(?:Z|[+-]\d{2}:\d{2}))"
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                ts = match.group(1).strip()
                try:
                    # Handle 'Z' suffix
                    if ts.endswith("Z"):
                        ts = ts[:-1] + "+00:00"
                    return datetime.fromisoformat(ts)
                except ValueError:
                    continue
        
        return None


class NewsExtractor:
    """Refactored, efficient news extraction pipeline."""
    
    def __init__(self):
        self.agent_factory = AgentFactory()
    
    def extract(self, data: ExtractInput) -> NewsItem:
        """Extract a NewsItem from raw article data."""
        
        # 1) Classification
        classification = self._extract_classification(data)
        
        # 2) Content
        content = self._extract_content(data)
        
        # 3) Type-specific details
        details = self._extract_details(classification.type, data.raw_content)
        
        # 4) Assemble NewsItem
        return NewsItem(
            source=Source(url=data.url, outlet=data.outlet),
            published_at=DateExtractor.extract_published_at(data.raw_content),
            retrieved_at=datetime.now(timezone.utc),
            language="en",
            classification=classification,
            content=content,
            details=details,
        )
    
    def _extract_classification(self, data: ExtractInput) -> Classification:
        """Extract classification information."""
        prompt = self._build_prompt(
            "Classify the article into policy taxonomy. Return a Classification object only.",
            data
        )
        return self.agent_factory.get_classification_agent().run_sync(prompt).output
    
    def _extract_content(self, data: ExtractInput) -> Content:
        """Extract normalized content."""
        prompt = self._build_prompt(
            "Extract normalized content. Return a Content object only.",
            data
        )
        return self.agent_factory.get_content_agent().run_sync(prompt).output
    
    def _extract_details(self, item_type: ItemType, text: str):
        """Extract type-specific details with error handling."""
        agent = self.agent_factory.get_detail_agent(item_type)
        if not agent:
            return None
        
        prompt = f"""Given the following article text, extract the specific fields requested by the schema.

Article text:
{text}
"""
        
        try:
            return agent.run_sync(prompt).output
        except Exception:
            # Graceful failure - return None if details extraction fails
            return None
    
    @staticmethod
    def _build_prompt(instruction: str, data: ExtractInput) -> str:
        """Build a standardized prompt."""
        return f"""{instruction}

URL: {data.url}
Outlet: {data.outlet or 'Unknown'}

Article text (may contain boilerplate):
{data.raw_content}
"""


__all__ = [
    "ExtractInput",
    "NewsExtractor",
]
