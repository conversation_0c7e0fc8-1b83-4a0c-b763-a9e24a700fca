from __future__ import annotations

from datetime import datetime, timedelta, timezone
from enum import Enum
from typing import List, Optional

from pydantic import BaseModel, Field, HttpUrl


class SourceType(str, Enum):
    DIRECT = "direct"           # Direct URL scraping (landing pages, RSS, etc.)
    WEB_SEARCH = "web_search"   # Web search queries (via Tavily)


class ExtractionMode(str, Enum):
    DIRECT = "direct"
    SEARCH = "search"


class NewsSource(BaseModel):
    """Configuration of a single news source."""

    id: str = Field(..., description="Unique source identifier")
    type: SourceType = Field(..., description="Source type: direct or web_search")

    # For direct sources
    url: Optional[HttpUrl] = Field(
        default=None, description="Landing page URL for direct scraping"
    )

    # For web search sources
    query: Optional[str] = Field(default=None, description="Web search query")

    # Optional hints/filters
    outlet: Optional[str] = Field(
        default=None, description="Publisher/outlet name to associate with results"
    )
    include_domains: Optional[List[str]] = Field(
        default=None, description="Limit results to these domains"
    )
    exclude_domains: Optional[List[str]] = Field(
        default=None, description="Exclude results from these domains"
    )
    instructions: Optional[str] = Field(
        default=None,
        description="Custom extraction instructions (e.g., regex for links, notes)",
    )
    time_window_hours: int = Field(
        default=24, ge=1, le=168, description="Time window for recency filtering"
    )
    max_results: int = Field(
        default=15, ge=1, le=100, description="Max articles to return for this source"
    )


class RawArticle(BaseModel):
    """Raw article fetched from a source, ready for downstream AI extraction."""

    url: HttpUrl
    outlet: Optional[str] = None
    raw_content: str
    published_at: Optional[datetime] = None


class ExtractionResult(BaseModel):
    """Per-source result with success flag and collected articles."""

    source_id: str
    success: bool
    articles: List[RawArticle] = Field(default_factory=list)
    error_message: Optional[str] = None


class NewsCollectionConfig(BaseModel):
    """Top-level YAML-configurable settings for collection."""

    time_window_hours: int = Field(
        default=24, ge=1, le=168, description="Default time window for all sources"
    )
    sources: List[NewsSource] = Field(default_factory=list)


class NewsCollectionResult(BaseModel):
    """Aggregate result from collecting all configured sources."""

    results: List[ExtractionResult] = Field(default_factory=list)

    @property
    def all_articles(self) -> List[RawArticle]:
        items: List[RawArticle] = []
        for r in self.results:
            items.extend(r.articles)
        return items

    @property
    def successful_sources(self) -> int:
        return sum(1 for r in self.results if r.success)

    @property
    def failed_sources(self) -> int:
        return sum(1 for r in self.results if not r.success)


__all__ = [
    "SourceType",
    "ExtractionMode",
    "NewsSource",
    "RawArticle",
    "ExtractionResult",
    "NewsCollectionConfig",
    "NewsCollectionResult",
]
