from __future__ import annotations

from datetime import datetime, timezone
from enum import Enum
from typing import List, Optional, Union
from uuid import UUID, uuid4

from pydantic import BaseModel, Field, AnyUrl, ConfigDict


class SourceType(str, Enum):
    """Type of news source to process."""
    WEB_SEARCH = "web_search"
    SPECIFIC_URL = "specific_url"
    RSS_FEED = "rss_feed"


class NewsSource(BaseModel):
    """Configuration for a news source."""

    model_config = ConfigDict(
        title="NewsSource",
        json_schema_extra={
            "description": "Configuration for a news source including type and extraction instructions."
        }
    )

    id: UUID = Field(default_factory=uuid4, description="Unique identifier for the source")
    name: str = Field(..., description="Human-readable name for the source")
    source_type: SourceType = Field(..., description="Type of source (web_search, specific_url, rss_feed)")

    # Source-specific configuration
    url: Optional[AnyUrl] = Field(default=None, description="URL for specific_url or rss_feed types")
    search_query: Optional[str] = Field(default=None, description="Search query for web_search type")
    search_domains: Optional[List[str]] = Field(default=None, description="Specific domains to search within")

    # Time filtering
    max_age_hours: int = Field(default=24, description="Maximum age of articles to collect (in hours)")

    # Extraction instructions for the AI agent
    extract_instructions: str = Field(
        ...,
        description="Detailed instructions for the AI agent on how to extract and filter content from this source"
    )

    # Optional metadata
    priority: int = Field(default=1, description="Priority level (1=highest, 5=lowest)")
    enabled: bool = Field(default=True, description="Whether this source is currently enabled")
    tags: Optional[List[str]] = Field(default=None, description="Optional tags for categorization")


class CollectionConfig(BaseModel):
    """Configuration for the news collection process."""

    model_config = ConfigDict(
        title="CollectionConfig",
        json_schema_extra={
            "description": "Configuration for the news collection process."
        }
    )

    sources: List[NewsSource] = Field(..., description="List of news sources to process")
    max_articles_per_source: int = Field(default=10, description="Maximum articles to collect per source")
    concurrent_sources: int = Field(default=3, description="Number of sources to process concurrently")

    # Global filtering
    exclude_domains: Optional[List[str]] = Field(default=None, description="Domains to exclude globally")
    required_keywords: Optional[List[str]] = Field(default=None, description="Keywords that must be present")
    excluded_keywords: Optional[List[str]] = Field(default=None, description="Keywords that exclude articles")


class RawArticle(BaseModel):
    """Raw article data before processing."""

    model_config = ConfigDict(
        title="RawArticle",
        json_schema_extra={
            "description": "Raw article data extracted from a source before AI processing."
        }
    )

    url: AnyUrl = Field(..., description="Article URL")
    title: Optional[str] = Field(default=None, description="Article title if available")
    content: str = Field(..., description="Raw article content")
    published_at: Optional[datetime] = Field(default=None, description="Publication date if available")
    source_name: str = Field(..., description="Name of the source this came from")
    source_id: UUID = Field(..., description="ID of the source configuration")
    extracted_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="When this was extracted")


class ProcessingResult(BaseModel):
    """Result of processing a news source."""

    model_config = ConfigDict(
        title="ProcessingResult",
        json_schema_extra={
            "description": "Result of processing a news source, including articles and any errors."
        }
    )

    source_id: UUID = Field(..., description="ID of the processed source")
    source_name: str = Field(..., description="Name of the processed source")
    articles: List[RawArticle] = Field(default_factory=list, description="Successfully extracted articles")
    errors: List[str] = Field(default_factory=list, description="Any errors encountered during processing")
    processed_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="When processing completed")
    processing_time_seconds: float = Field(..., description="Time taken to process this source")


class CollectionRun(BaseModel):
    """Complete collection run results."""

    model_config = ConfigDict(
        title="CollectionRun",
        json_schema_extra={
            "description": "Results from a complete news collection run across all sources."
        }
    )

    id: UUID = Field(default_factory=uuid4, description="Unique identifier for this collection run")
    started_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="When collection started")
    completed_at: Optional[datetime] = Field(default=None, description="When collection completed")
    config: CollectionConfig = Field(..., description="Configuration used for this run")
    results: List[ProcessingResult] = Field(default_factory=list, description="Results from each source")
    total_articles: int = Field(default=0, description="Total articles collected")
    total_errors: int = Field(default=0, description="Total errors encountered")


__all__ = [
    "SourceType",
    "NewsSource",
    "CollectionConfig",
    "RawArticle",
    "ProcessingResult",
    "CollectionRun",
]