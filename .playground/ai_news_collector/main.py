#!/usr/bin/env python3
"""
AI News Collector - Main entry point

This script demonstrates how to use the AI news collector system to gather
carbon regulation and clean energy news from various sources.
"""

import asyncio
import json
import os
import sys
from pathlib import Path
from typing import List

import yaml
from pydantic import ValidationError

# Add the parent directory to the path to import from ai_source_parser
current_dir = Path(__file__).parent
parent_dir = current_dir.parent
sys.path.append(str(parent_dir))

from ai_news_collector.models import CollectionConfig, NewsSource
from ai_news_collector.agents import NewsCollectionOrchestrator
from ai_source_parser.news_pipeline_refactored import NewsExtractor, ExtractInput


def load_config_from_yaml(config_path: str) -> CollectionConfig:
    """Load configuration from YAML file."""
    with open(config_path, 'r') as f:
        config_data = yaml.safe_load(f)

    # Convert sources to NewsSource objects
    sources = []
    for source_data in config_data.get('sources', []):
        try:
            source = NewsSource(**source_data)
            sources.append(source)
        except ValidationError as e:
            print(f"Error parsing source {source_data.get('name', 'unknown')}: {e}")
            continue

    # Create CollectionConfig
    config = CollectionConfig(
        sources=sources,
        max_articles_per_source=config_data.get('max_articles_per_source', 10),
        concurrent_sources=config_data.get('concurrent_sources', 3),
        exclude_domains=config_data.get('exclude_domains'),
        required_keywords=config_data.get('required_keywords'),
        excluded_keywords=config_data.get('excluded_keywords')
    )

    return config


async def collect_and_process_news(config_path: str = None) -> List[dict]:
    """
    Main function to collect news and process it through the existing pipeline.

    Returns a list of processed NewsItem dictionaries.
    """
    # Load configuration
    if config_path is None:
        config_path = current_dir / "config.yaml"

    print(f"Loading configuration from {config_path}")
    config = load_config_from_yaml(config_path)

    print(f"Loaded {len(config.sources)} sources")

    # Initialize the orchestrator
    orchestrator = NewsCollectionOrchestrator()

    # Collect news
    print("Starting news collection...")
    collection_run = await orchestrator.collect_news(config)

    print(f"Collection completed:")
    print(f"  - Total articles: {collection_run.total_articles}")
    print(f"  - Total errors: {collection_run.total_errors}")
    print(f"  - Processing time: {(collection_run.completed_at - collection_run.started_at).total_seconds():.2f}s")

    # Get all articles
    raw_articles = orchestrator.get_all_articles(collection_run)

    if not raw_articles:
        print("No articles collected.")
        return []

    # Process articles through the existing news pipeline
    print(f"Processing {len(raw_articles)} articles through AI pipeline...")

    news_extractor = NewsExtractor()
    processed_articles = []

    for raw_article in raw_articles:
        try:
            # Convert RawArticle to ExtractInput
            extract_input = ExtractInput(
                url=str(raw_article.url),
                outlet=raw_article.source_name,
                raw_content=raw_article.content
            )

            # Process through the news pipeline
            news_item = news_extractor.extract(extract_input)

            # Convert to dict for easier handling
            processed_articles.append(news_item.model_dump())

            print(f"  ✓ Processed: {news_item.content.title}")

        except Exception as e:
            print(f"  ✗ Error processing article from {raw_article.url}: {e}")
            continue

    print(f"Successfully processed {len(processed_articles)} articles")
    return processed_articles


async def main():
    """Main entry point."""

    # Check for required environment variables
    required_env_vars = ["OPENROUTER_API_KEY"]
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]

    if missing_vars:
        print(f"Error: Missing required environment variables: {', '.join(missing_vars)}")
        print("Please set these in your .env file or environment.")
        return

    # Optional: Check for Tavily API key
    if not os.getenv("TAVILY_API_KEY"):
        print("Warning: TAVILY_API_KEY not found. Web search functionality will be disabled.")

    try:
        # Run the collection and processing
        processed_articles = await collect_and_process_news()

        if processed_articles:
            # Save results to file
            output_file = current_dir / "collected_news.json"
            with open(output_file, 'w') as f:
                json.dump(processed_articles, f, indent=2, default=str)

            print(f"Results saved to {output_file}")

            # Print summary
            print("\nCollection Summary:")
            for article in processed_articles:
                print(f"  - {article['content']['title']}")
                print(f"    Category: {article['classification']['category']}")
                print(f"    Type: {article['classification']['type']}")
                print(f"    Source: {article['source']['outlet']}")
                print()

    except Exception as e:
        print(f"Error during execution: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())