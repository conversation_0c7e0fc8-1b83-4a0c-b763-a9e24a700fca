"""
AI News Collector

A pydantic-ai based system for collecting and processing news from multiple sources.
"""

from .models import (
    NewsSource,
    RawArticle,
    ExtractionResult,
    NewsCollectionConfig,
    NewsCollectionResult,
    SourceType,
    ExtractionMode,
)

from .agents import (
    NewsExtractionAgent,
    NewsCoordinatorAgent,
    SimpleNewsCollector,
    create_news_collector,
    NewsCollectorTool,
    create_news_collector_tool,
)

from .tools import (
    NewsCollectionTools,
    ALL_NEWS_TOOL_FUNCTIONS,
)

__all__ = [
    # Models
    "NewsSource",
    "RawArticle", 
    "ExtractionResult",
    "NewsCollectionConfig",
    "NewsCollectionResult",
    "SourceType",
    "ExtractionMode",
    
    # Agents
    "NewsExtractionAgent",
    "NewsCoordinatorAgent", 
    "SimpleNewsCollector",
    "create_news_collector",
    "NewsCollectorTool",
    "create_news_collector_tool",
    
    # Tools
    "NewsCollectionTools",
    "ALL_NEWS_TOOL_FUNCTIONS",
]
