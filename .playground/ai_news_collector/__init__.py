"""
AI News Collector

A pydantic-ai based system for collecting and processing carbon regulation and clean energy news
from various sources including web search, specific URLs, and RSS feeds.
"""

from .models import (
    SourceType,
    NewsSource,
    CollectionConfig,
    RawArticle,
    ProcessingResult,
    CollectionRun,
)

from .agents import (
    ModelManager,
    NewsCollectorAgent,
    SourceProcessorAgent,
    NewsCollectionOrchestrator,
)

from .tools import (
    SearchResult,
    ExtractedContent,
    DateComparisonResult,
    web_search_tool,
    website_extraction_tool,
    date_comparison_tool,
    url_list_extraction_tool,
    content_filter_tool,
)

__version__ = "0.1.0"

__all__ = [
    # Models
    "SourceType",
    "NewsSource",
    "CollectionConfig",
    "RawArticle",
    "ProcessingResult",
    "CollectionRun",

    # Agents
    "ModelManager",
    "NewsCollectorAgent",
    "SourceProcessorAgent",
    "NewsCollectionOrchestrator",

    # Tools
    "SearchResult",
    "ExtractedContent",
    "DateComparisonResult",
    "web_search_tool",
    "website_extraction_tool",
    "date_comparison_tool",
    "url_list_extraction_tool",
    "content_filter_tool",
]
