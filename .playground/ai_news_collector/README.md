# AI News Collector

A sophisticated pydantic-ai based system for collecting and processing carbon regulation and clean energy news from various sources.

## Overview

This system uses AI agents with specialized tools to:
- Search the web for relevant news articles
- Extract content from specific URLs (like Reuters clean energy page)
- Filter articles by date and relevance
- Process content through the existing news pipeline
- Return structured NewsItem objects

## Features

- **Multi-source support**: Web search, specific URLs, RSS feeds
- **AI-powered extraction**: Uses pydantic-ai agents with specialized tools
- **Configurable timeframes**: Filter articles by publication date
- **Content filtering**: Keyword-based filtering and relevance checking
- **Concurrent processing**: Process multiple sources simultaneously
- **Error handling**: Graceful error handling with detailed reporting
- **Pipeline integration**: Seamlessly integrates with existing NewsExtractor

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   config.yaml   │───▶│ NewsCollector    │───▶│ NewsExtractor   │
│                 │    │ Orchestrator     │    │ Pipeline        │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                              ▼
                    ┌──────────────────┐
                    │ SourceProcessor  │
                    │ Agents           │
                    └──────────────────┘
                              │
                              ▼
                    ┌──────────────────┐
                    │ Specialized      │
                    │ Tools            │
                    └──────────────────┘
```

## Quick Start

1. **Set up environment variables**:
   ```bash
   export OPENROUTER_API_KEY="your-openrouter-key"
   export TAVILY_API_KEY="your-tavily-key"  # Optional, for web search
   ```

2. **Configure sources** in `config.yaml`:
   ```yaml
   sources:
     - name: "Reuters Clean Energy"
       source_type: "specific_url"
       url: "https://www.reuters.com/sustainability/clean-energy/"
       max_age_hours: 24
       extract_instructions: |
         Extract recent clean energy articles from the Reuters page.
         Use extract_article_urls to find links, then extract content
         from each URL and filter by date and relevance.
   ```

3. **Run the collector**:
   ```bash
   python main.py
   ```

4. **Check results** in `collected_news.json`

## Configuration

### Source Types

- **`web_search`**: Search the web using Tavily API
- **`specific_url`**: Extract articles from a specific page
- **`rss_feed`**: Process RSS feeds (uses URL extraction)

### Extract Instructions

Each source has detailed `extract_instructions` that tell the AI agent:
- Which tools to use
- How to filter content
- What to look for
- How to process the results

Example for Reuters clean energy page:
```yaml
extract_instructions: |
  Extract articles from the Reuters clean energy page that are from the last 24 hours.
  
  Steps:
  1. Use extract_article_urls to find all article links on the page
  2. For each article URL, use extract_website_content to get the full content
  3. Use compare_date_with_timeframe to check if the article is within 24 hours
  4. Use filter_content to ensure articles are relevant to clean energy
  5. Return only articles that pass all filters
```

## Available Tools

The AI agents have access to these tools:

- **`web_search`**: Search the web for news articles
- **`extract_website_content`**: Extract content from URLs
- **`extract_article_urls`**: Find article links on pages
- **`compare_date_with_timeframe`**: Check article freshness
- **`filter_content`**: Filter by keywords and criteria

## Usage Examples

### Programmatic Usage

```python
from ai_news_collector import (
    NewsSource, CollectionConfig, NewsCollectionOrchestrator, SourceType
)

# Create a source
source = NewsSource(
    name="Reuters Clean Energy",
    source_type=SourceType.SPECIFIC_URL,
    url="https://www.reuters.com/sustainability/clean-energy/",
    max_age_hours=24,
    extract_instructions="Extract recent clean energy articles..."
)

# Create configuration
config = CollectionConfig(sources=[source])

# Run collection
orchestrator = NewsCollectionOrchestrator()
collection_run = await orchestrator.collect_news(config)

# Get articles
articles = orchestrator.get_all_articles(collection_run)
```

### Integration with News Pipeline

```python
from ai_source_parser.news_pipeline_refactored import NewsExtractor, ExtractInput

# Process through existing pipeline
news_extractor = NewsExtractor()

for raw_article in articles:
    extract_input = ExtractInput(
        url=str(raw_article.url),
        outlet=raw_article.source_name,
        raw_content=raw_article.content
    )
    
    news_item = news_extractor.extract(extract_input)
    # news_item is now a structured NewsItem object
```

## Testing

Run the test suite to verify everything works:

```bash
python test_system.py
```

Run a simple example:

```bash
python example.py
```

## Requirements

- Python 3.8+
- pydantic-ai
- requests
- beautifulsoup4
- pyyaml
- tavily-python (optional, for web search)

## Environment Variables

- `OPENROUTER_API_KEY`: Required for AI model access
- `TAVILY_API_KEY`: Optional, enables web search functionality

## Error Handling

The system includes comprehensive error handling:
- Individual source failures don't stop the entire collection
- Detailed error reporting in results
- Graceful degradation when optional features are unavailable

## Extending the System

### Adding New Source Types

1. Add the source type to `SourceType` enum in `models.py`
2. Create a specialized agent in `agents.py`
3. Update the `SourceProcessorAgent` to handle the new type

### Adding New Tools

1. Create the tool function in `tools.py`
2. Add it to the appropriate agent's tool list
3. Update the system prompt to describe the new tool

## License

This project is part of the carbon regulation news system.
