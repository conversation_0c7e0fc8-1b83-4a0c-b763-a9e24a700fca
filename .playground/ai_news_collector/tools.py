from __future__ import annotations

import re
from datetime import datetime, timezone
from typing import List, Optional
from urllib.parse import urljoin, urlparse

# Note: requests and BeautifulSoup are still used by url_list_extraction_tool
import requests
from bs4 import BeautifulSoup
from pydantic import BaseModel, Field
from pydantic_ai import RunContext
from pydantic_ai.tools import Tool

from .models import RawArticle, NewsSource


class SearchResult(BaseModel):
    """Result from web search."""
    url: str
    title: str
    snippet: str
    published_date: Optional[datetime] = None


class ExtractedContent(BaseModel):
    """Content extracted from a webpage."""
    url: str
    title: Optional[str]
    content: str
    published_date: Optional[datetime]
    raw_html: str


class DateComparisonResult(BaseModel):
    """Result of date comparison."""
    is_within_timeframe: bool
    age_hours: Optional[float]
    reason: str


def web_search_tool(api_key: str) -> Tool:
    """Create a web search tool using Tavily API."""

    def search_web(
        ctx: RunContext,
        query: str,
        max_results: int = 3,
        include_domains: Optional[List[str]] = None,
        exclude_domains: Optional[List[str]] = None,
        max_age_hours: int = 24
    ) -> List[SearchResult]:
        """
        Search the web for news articles.

        Args:
            query: Search query
            max_results: Maximum number of results to return
            include_domains: List of domains to include in search
            exclude_domains: List of domains to exclude from search
            max_age_hours: Maximum age of articles in hours
        """
        try:
            from tavily import TavilyClient

            client = TavilyClient(api_key)

            # Determine time range
            time_range = "day" if max_age_hours <= 24 else "week" if max_age_hours <= 168 else "month"

            response = client.search(
                query=query,
                topic="news",
                search_depth="basic",
                time_range=time_range,
                max_results=max_results,
                include_domains=include_domains or [],
                exclude_domains=exclude_domains or []
            )

            results = []
            for result in response.get("results", []):
                # Parse published date if available
                published_date = None
                if "published_date" in result:
                    try:
                        published_date = datetime.fromisoformat(result["published_date"].replace("Z", "+00:00"))
                    except (ValueError, AttributeError):
                        pass

                results.append(SearchResult(
                    url=result["url"],
                    title=result["title"],
                    snippet=result.get("content", ""),
                    published_date=published_date
                ))

            return results

        except Exception as e:
            print(f"Web search error: {e}")
            return []

    return Tool(search_web, takes_ctx=True)


def website_extraction_tool(api_key: str) -> Tool:
    """Create a tool for extracting content from websites using Tavily."""

    def extract_website_content(
        ctx: RunContext,
        url: str,
        extract_depth: str = "basic"
    ) -> Optional[ExtractedContent]:
        """
        Extract content from a website URL using Tavily.

        Args:
            url: URL to extract content from
            extract_depth: Extraction depth - 'basic' or 'advanced'
        """
        try:
            from tavily import TavilyClient

            client = TavilyClient(api_key)

            response = client.extract(
                urls=[url],
                extract_depth=extract_depth,
                format="markdown"
            )

            if not response.get("results"):
                return None

            result = response["results"][0]
            raw_content = result.get("raw_content", "")

            # Extract title from the raw content (look for first line or title pattern)
            title = None
            lines = raw_content.split('\n')
            for line in lines:
                line = line.strip()
                if line and not line.startswith('Published Time:'):
                    # Look for title-like patterns
                    if ' | ' in line and len(line) < 200:
                        title = line.split(' | ')[0].strip()
                        break
                    elif len(line) < 200 and not line.startswith('http'):
                        title = line
                        break

            # Try to extract published date from raw content
            published_date = None
            date_patterns = [
                r'Published Time:\s*([0-9T:\-.+Z]+)',
                r'(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?(?:Z|[+-]\d{2}:\d{2}))',
                r'(\d{4}-\d{2}-\d{2})'
            ]

            for pattern in date_patterns:
                match = re.search(pattern, raw_content)
                if match:
                    try:
                        date_str = match.group(1)
                        if date_str.endswith('Z'):
                            date_str = date_str[:-1] + '+00:00'
                        published_date = datetime.fromisoformat(date_str)
                        break
                    except ValueError:
                        continue

            return ExtractedContent(
                url=url,
                title=title,
                content=raw_content,
                published_date=published_date,
                raw_html=raw_content  # Tavily returns processed content, not raw HTML
            )

        except Exception as e:
            print(f"Website extraction error for {url}: {e}")
            return None

    return Tool(extract_website_content, takes_ctx=True)


def date_comparison_tool() -> Tool:
    """Create a tool for comparing dates and checking if content is within timeframe."""

    def compare_date_with_timeframe(
        ctx: RunContext,
        article_date: Optional[datetime],
        max_age_hours: int,
        current_time: Optional[datetime] = None
    ) -> DateComparisonResult:
        """
        Compare an article's date with the maximum age requirement.

        Args:
            article_date: The publication date of the article
            max_age_hours: Maximum age in hours
            current_time: Current time (defaults to now)
        """
        if current_time is None:
            current_time = datetime.now(timezone.utc)

        if article_date is None:
            return DateComparisonResult(
                is_within_timeframe=False,
                age_hours=None,
                reason="No publication date available"
            )

        # Ensure article_date is timezone-aware
        if article_date.tzinfo is None:
            article_date = article_date.replace(tzinfo=timezone.utc)

        # Calculate age
        age = current_time - article_date
        age_hours = age.total_seconds() / 3600

        is_within_timeframe = age_hours <= max_age_hours

        return DateComparisonResult(
            is_within_timeframe=is_within_timeframe,
            age_hours=age_hours,
            reason=f"Article is {age_hours:.1f} hours old, limit is {max_age_hours} hours"
        )

    return Tool(compare_date_with_timeframe, takes_ctx=True)


def url_list_extraction_tool() -> Tool:
    """Create a tool for extracting multiple article URLs from a page (like Reuters clean energy page)."""

    def extract_article_urls(
        ctx: RunContext,
        page_url: str,
        max_urls: int = 10,
        url_pattern: Optional[str] = None
    ) -> List[str]:
        """
        Extract article URLs from a page.

        Args:
            page_url: URL of the page to extract links from
            max_urls: Maximum number of URLs to extract
            url_pattern: Optional regex pattern to filter URLs
        """
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            response = requests.get(page_url, headers=headers, timeout=30)
            response.raise_for_status()

            soup = BeautifulSoup(response.content, 'html.parser')

            # Find all links
            links = soup.find_all('a', href=True)

            urls = []
            base_domain = urlparse(page_url).netloc

            for link in links:
                href = link['href']

                # Convert relative URLs to absolute
                if href.startswith('/'):
                    full_url = urljoin(page_url, href)
                elif href.startswith('http'):
                    full_url = href
                else:
                    continue

                # Filter by pattern if provided
                if url_pattern and not re.search(url_pattern, full_url):
                    continue

                # Basic filtering for article-like URLs
                if any(keyword in full_url.lower() for keyword in ['/article/', '/news/', '/story/', '/post/']):
                    urls.append(full_url)
                elif base_domain in full_url and len(urlparse(full_url).path.split('/')) > 2:
                    urls.append(full_url)

                if len(urls) >= max_urls:
                    break

            return list(set(urls))  # Remove duplicates

        except Exception as e:
            print(f"URL extraction error for {page_url}: {e}")
            return []

    return Tool(extract_article_urls, takes_ctx=True)


def content_filter_tool() -> Tool:
    """Create a tool for filtering content based on keywords and other criteria."""

    def filter_content(
        ctx: RunContext,
        content: str,
        title: str,
        required_keywords: Optional[List[str]] = None,
        excluded_keywords: Optional[List[str]] = None,
        min_content_length: int = 100
    ) -> bool:
        """
        Filter content based on various criteria.

        Args:
            content: Article content
            title: Article title
            required_keywords: Keywords that must be present
            excluded_keywords: Keywords that exclude the article
            min_content_length: Minimum content length
        """
        full_text = f"{title} {content}".lower()

        # Check minimum length
        if len(content) < min_content_length:
            return False

        # Check required keywords
        if required_keywords:
            if not any(keyword.lower() in full_text for keyword in required_keywords):
                return False

        # Check excluded keywords
        if excluded_keywords:
            if any(keyword.lower() in full_text for keyword in excluded_keywords):
                return False

        return True

    return Tool(filter_content, takes_ctx=True)


__all__ = [
    "SearchResult",
    "ExtractedContent",
    "DateComparisonResult",
    "web_search_tool",
    "website_extraction_tool",
    "date_comparison_tool",
    "url_list_extraction_tool",
    "content_filter_tool",
]