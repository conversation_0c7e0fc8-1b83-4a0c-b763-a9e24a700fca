#!/usr/bin/env python3
"""
Test script to verify the AI News Collector system works correctly.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add parent directory to path
current_dir = Path(__file__).parent
parent_dir = current_dir.parent
sys.path.append(str(parent_dir))

from ai_news_collector import (
    NewsSource,
    CollectionConfig,
    NewsCollectionOrchestrator,
    SourceType
)


async def test_basic_functionality():
    """Test basic functionality without external dependencies."""
    
    print("Testing AI News Collector System...")
    print("=" * 50)
    
    # Test 1: Model creation
    print("✓ Testing model creation...")
    
    source = NewsSource(
        name="Test Source",
        source_type=SourceType.SPECIFIC_URL,
        url="https://example.com",
        max_age_hours=24,
        extract_instructions="Test instructions"
    )
    
    config = CollectionConfig(
        sources=[source],
        max_articles_per_source=5
    )
    
    print(f"  - Created source: {source.name}")
    print(f"  - Source type: {source.source_type}")
    print(f"  - Config has {len(config.sources)} sources")
    
    # Test 2: Orchestrator creation
    print("\n✓ Testing orchestrator creation...")
    
    try:
        orchestrator = NewsCollectionOrchestrator()
        print("  - Orchestrator created successfully")
    except Exception as e:
        print(f"  ✗ Error creating orchestrator: {e}")
        return False
    
    # Test 3: Tool availability
    print("\n✓ Testing tool availability...")
    
    processor = orchestrator.processor
    
    if hasattr(processor, 'url_extraction_agent'):
        print("  - URL extraction agent available")
    
    if hasattr(processor, 'web_search_agent'):
        print("  - Web search agent available")
    else:
        print("  - Web search agent not available (missing Tavily API key)")
    
    print("\n✓ Basic functionality test completed successfully!")
    return True


async def test_with_mock_data():
    """Test with mock data to verify the pipeline works."""
    
    print("\n" + "=" * 50)
    print("Testing with mock data...")
    
    # Create a simple test source
    test_source = NewsSource(
        name="Mock Test Source",
        source_type=SourceType.SPECIFIC_URL,
        url="https://httpbin.org/html",  # Simple test URL
        max_age_hours=24,
        extract_instructions="""
        This is a test source. Extract any content found and return it as a RawArticle.
        Use the website extraction tool to get content from the URL.
        """
    )
    
    config = CollectionConfig(
        sources=[test_source],
        max_articles_per_source=1,
        concurrent_sources=1
    )
    
    orchestrator = NewsCollectionOrchestrator()
    
    try:
        print("Running collection with test source...")
        collection_run = await orchestrator.collect_news(config)
        
        print(f"Collection results:")
        print(f"  - Total articles: {collection_run.total_articles}")
        print(f"  - Total errors: {collection_run.total_errors}")
        print(f"  - Sources processed: {len(collection_run.results)}")
        
        for result in collection_run.results:
            print(f"  - {result.source_name}: {len(result.articles)} articles, {len(result.errors)} errors")
            if result.errors:
                for error in result.errors:
                    print(f"    Error: {error}")
        
        return len(collection_run.results) > 0
        
    except Exception as e:
        print(f"✗ Error during collection: {e}")
        import traceback
        traceback.print_exc()
        return False


def check_environment():
    """Check if required environment variables are set."""
    
    print("Checking environment...")
    print("=" * 50)
    
    required_vars = ["OPENROUTER_API_KEY"]
    optional_vars = ["TAVILY_API_KEY"]
    
    all_good = True
    
    for var in required_vars:
        if os.getenv(var):
            print(f"✓ {var} is set")
        else:
            print(f"✗ {var} is missing (required)")
            all_good = False
    
    for var in optional_vars:
        if os.getenv(var):
            print(f"✓ {var} is set")
        else:
            print(f"⚠ {var} is missing (optional - disables web search)")
    
    return all_good


async def main():
    """Main test function."""
    
    print("AI News Collector System Test")
    print("=" * 50)
    
    # Check environment
    if not check_environment():
        print("\n✗ Environment check failed. Please set required environment variables.")
        return
    
    # Run basic tests
    if not await test_basic_functionality():
        print("\n✗ Basic functionality test failed.")
        return
    
    # Run mock data test
    if await test_with_mock_data():
        print("\n✓ Mock data test completed successfully!")
    else:
        print("\n⚠ Mock data test had issues (this may be expected)")
    
    print("\n" + "=" * 50)
    print("Test Summary:")
    print("✓ Models and configuration work correctly")
    print("✓ Orchestrator can be created")
    print("✓ System is ready for use")
    print("\nTo use the system:")
    print("1. Update config.yaml with your desired sources")
    print("2. Run: python main.py")
    print("3. Check the output for collected articles")


if __name__ == "__main__":
    asyncio.run(main())
