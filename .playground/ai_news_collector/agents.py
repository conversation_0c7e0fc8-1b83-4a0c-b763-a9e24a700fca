from __future__ import annotations

import asyncio
import os
import time
from datetime import datetime, timezone
from typing import List, Optional, Dict, Any

from pydantic_ai import Agent, RunContext
from pydantic_ai.models.openai import OpenAIChatModel
from pydantic_ai.providers.openrouter import OpenRouterProvider

from .models import (
    NewsSource,
    RawArticle,
    ProcessingResult,
    CollectionConfig,
    CollectionRun,
    SourceType
)
from .tools import (
    web_search_tool,
    website_extraction_tool,
    date_comparison_tool,
    url_list_extraction_tool,
    content_filter_tool,
    SearchResult,
    ExtractedContent,
    DateComparisonResult
)


class ModelManager:
    """Singleton model manager for AI agents."""

    _instance: Optional[OpenAIChatModel] = None

    @classmethod
    def get_model(cls) -> OpenAIChatModel:
        if cls._instance is None:
            api_key = os.getenv("OPENROUTER_API_KEY")
            if not api_key:
                raise RuntimeError("Missing OPENROUTER_API_KEY in environment")

            cls._instance = OpenAIChatModel(
                "openai/gpt-4o-mini",
                provider=OpenRouterProvider(api_key=api_key),
            )
        return cls._instance


class NewsCollectorAgent:
    """Main agent for collecting news from various sources."""

    def __init__(self, tavily_api_key: Optional[str] = None):
        self.model = ModelManager.get_model()
        self.tavily_api_key = tavily_api_key or os.getenv("TAVILY_API_KEY")

        # Create tools
        self.web_search = web_search_tool(self.tavily_api_key) if self.tavily_api_key else None
        self.website_extraction = website_extraction_tool()
        self.date_comparison = date_comparison_tool()
        self.url_extraction = url_list_extraction_tool()
        self.content_filter = content_filter_tool()

        # Create the main agent
        tools = [self.website_extraction, self.date_comparison, self.url_extraction, self.content_filter]
        if self.web_search:
            tools.append(self.web_search)

        self.agent = Agent(
            self.model,
            deps_type=NewsSource,
            output_type=List[RawArticle],
            tools=tools,
            system_prompt=self._get_system_prompt()
        )

    def _get_system_prompt(self) -> str:
        return """You are a news collection agent specialized in gathering carbon regulation and clean energy news.

Your task is to process a news source configuration and extract relevant articles based on the provided instructions.

Available tools:
- web_search: Search the web for news articles (if Tavily API is available)
- extract_website_content: Extract content from a specific URL
- extract_article_urls: Extract multiple article URLs from a page
- compare_date_with_timeframe: Check if an article is within the required timeframe
- filter_content: Filter content based on keywords and criteria

For each source, you should:
1. Follow the extract_instructions provided in the source configuration
2. Respect the max_age_hours limit for article freshness
3. Extract clean, relevant content
4. Return a list of RawArticle objects

Be thorough but efficient. Focus on high-quality, relevant articles."""

    async def process_source(self, source: NewsSource) -> ProcessingResult:
        """Process a single news source and return results."""
        start_time = time.time()

        try:
            # Run the agent with the source configuration
            result = await self.agent.run(
                f"Process this news source according to its instructions: {source.extract_instructions}",
                deps=source
            )

            articles = result.output if result.output else []

            return ProcessingResult(
                source_id=source.id,
                source_name=source.name,
                articles=articles,
                errors=[],
                processing_time_seconds=time.time() - start_time
            )

        except Exception as e:
            return ProcessingResult(
                source_id=source.id,
                source_name=source.name,
                articles=[],
                errors=[str(e)],
                processing_time_seconds=time.time() - start_time
            )


class SourceProcessorAgent:
    """Specialized agent for processing different types of sources."""

    def __init__(self, tavily_api_key: Optional[str] = None):
        self.model = ModelManager.get_model()
        self.tavily_api_key = tavily_api_key or os.getenv("TAVILY_API_KEY")

        # Create specialized agents for different source types
        self._create_agents()

    def _create_agents(self):
        """Create specialized agents for different source types."""

        # Web search agent
        if self.tavily_api_key:
            self.web_search_agent = Agent(
                self.model,
                deps_type=NewsSource,
                output_type=List[RawArticle],
                tools=[web_search_tool(self.tavily_api_key), website_extraction_tool(),
                       date_comparison_tool(), content_filter_tool()],
                system_prompt="""You are a web search specialist for news collection.

Use the web_search tool to find relevant articles based on the source's search_query.
Then extract content from promising URLs and filter based on the source requirements.
Focus on recent, high-quality articles that match the search criteria."""
            )

        # URL extraction agent
        self.url_extraction_agent = Agent(
            self.model,
            deps_type=NewsSource,
            output_type=List[RawArticle],
            tools=[url_list_extraction_tool(), website_extraction_tool(),
                   date_comparison_tool(), content_filter_tool()],
            system_prompt="""You are a URL extraction specialist for news collection.

Use the extract_article_urls tool to find article links from the provided URL.
Then extract content from each URL and filter based on date and content requirements.
Focus on extracting clean, relevant content from each article."""
        )

    async def process_source(self, source: NewsSource) -> ProcessingResult:
        """Process a source using the appropriate specialized agent."""
        start_time = time.time()

        try:
            if source.source_type == SourceType.WEB_SEARCH:
                if not hasattr(self, 'web_search_agent'):
                    raise ValueError("Web search not available - missing Tavily API key")
                agent = self.web_search_agent
            elif source.source_type == SourceType.SPECIFIC_URL:
                agent = self.url_extraction_agent
            else:
                # For RSS feeds, use URL extraction agent as fallback
                agent = self.url_extraction_agent

            result = await agent.run(
                f"Process this {source.source_type.value} source: {source.extract_instructions}",
                deps=source
            )

            articles = result.output if result.output else []

            return ProcessingResult(
                source_id=source.id,
                source_name=source.name,
                articles=articles,
                errors=[],
                processing_time_seconds=time.time() - start_time
            )

        except Exception as e:
            return ProcessingResult(
                source_id=source.id,
                source_name=source.name,
                articles=[],
                errors=[str(e)],
                processing_time_seconds=time.time() - start_time
            )


class NewsCollectionOrchestrator:
    """Main orchestrator for the news collection process."""

    def __init__(self, tavily_api_key: Optional[str] = None):
        self.processor = SourceProcessorAgent(tavily_api_key)

    async def collect_news(self, config: CollectionConfig) -> CollectionRun:
        """Run a complete news collection based on the configuration."""

        collection_run = CollectionRun(config=config)

        # Filter enabled sources and sort by priority
        enabled_sources = [s for s in config.sources if s.enabled]
        enabled_sources.sort(key=lambda x: x.priority)

        # Process sources with concurrency control
        semaphore = asyncio.Semaphore(config.concurrent_sources)

        async def process_single_source(source: NewsSource) -> ProcessingResult:
            async with semaphore:
                return await self.processor.process_source(source)

        # Create tasks for all sources
        tasks = [process_single_source(source) for source in enabled_sources]

        # Execute all tasks
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Process results
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                # Handle exceptions
                source = enabled_sources[i]
                error_result = ProcessingResult(
                    source_id=source.id,
                    source_name=source.name,
                    articles=[],
                    errors=[str(result)],
                    processing_time_seconds=0.0
                )
                collection_run.results.append(error_result)
            else:
                collection_run.results.append(result)

        # Calculate totals
        collection_run.total_articles = sum(len(r.articles) for r in collection_run.results)
        collection_run.total_errors = sum(len(r.errors) for r in collection_run.results)
        collection_run.completed_at = datetime.now(timezone.utc)

        return collection_run

    def get_all_articles(self, collection_run: CollectionRun) -> List[RawArticle]:
        """Extract all articles from a collection run."""
        articles = []
        for result in collection_run.results:
            articles.extend(result.articles)
        return articles


__all__ = [
    "ModelManager",
    "NewsCollectorAgent",
    "SourceProcessorAgent",
    "NewsCollectionOrchestrator",
]