#!/usr/bin/env python3
"""
Simple example demonstrating the AI News Collector system.

This example shows how to:
1. Create news sources programmatically
2. Run the collection process
3. Process results through the existing news pipeline
"""

import asyncio
import os
import sys
from pathlib import Path

# Add parent directory to path for imports
current_dir = Path(__file__).parent
parent_dir = current_dir.parent
sys.path.append(str(parent_dir))

from ai_news_collector import (
    NewsSource,
    CollectionConfig,
    NewsCollectionOrchestrator,
    SourceType
)
from ai_source_parser.news_pipeline_refactored import NewsExtractor, ExtractInput


async def simple_example():
    """Simple example of collecting news from a specific URL."""
    
    print("=== AI News Collector Example ===\n")
    
    # Create a simple news source configuration
    reuters_source = NewsSource(
        name="Reuters Clean Energy",
        source_type=SourceType.SPECIFIC_URL,
        url="https://www.reuters.com/sustainability/clean-energy/",
        max_age_hours=24,
        extract_instructions="""
        Extract recent clean energy articles from the Reuters page.
        
        Steps:
        1. Use extract_article_urls to find article links on the page
        2. For each URL, use extract_website_content to get the content
        3. Use compare_date_with_timeframe to check if articles are within 24 hours
        4. Use filter_content to ensure relevance to clean energy topics
        5. Return articles that pass all filters
        
        Focus on articles about renewable energy, clean energy policy, and energy transition.
        """
    )
    
    # Create collection configuration
    config = CollectionConfig(
        sources=[reuters_source],
        max_articles_per_source=5,
        concurrent_sources=1
    )
    
    # Initialize orchestrator
    orchestrator = NewsCollectionOrchestrator()
    
    print("Starting news collection...")
    
    try:
        # Run collection
        collection_run = await orchestrator.collect_news(config)
        
        print(f"Collection completed:")
        print(f"  - Total articles: {collection_run.total_articles}")
        print(f"  - Total errors: {collection_run.total_errors}")
        
        # Get articles
        raw_articles = orchestrator.get_all_articles(collection_run)
        
        if raw_articles:
            print(f"\nFound {len(raw_articles)} articles:")
            for i, article in enumerate(raw_articles, 1):
                print(f"  {i}. {article.title or 'No title'}")
                print(f"     URL: {article.url}")
                print(f"     Source: {article.source_name}")
                print()
            
            # Process through existing pipeline
            print("Processing through AI pipeline...")
            news_extractor = NewsExtractor()
            
            for article in raw_articles[:2]:  # Process first 2 articles as example
                try:
                    extract_input = ExtractInput(
                        url=str(article.url),
                        outlet=article.source_name,
                        raw_content=article.content
                    )
                    
                    news_item = news_extractor.extract(extract_input)
                    
                    print(f"\n✓ Processed: {news_item.content.title}")
                    print(f"  Category: {news_item.classification.category}")
                    print(f"  Type: {news_item.classification.type}")
                    print(f"  Summary: {news_item.content.summary}")
                    
                except Exception as e:
                    print(f"✗ Error processing article: {e}")
        else:
            print("No articles found.")
            
    except Exception as e:
        print(f"Error during collection: {e}")
        import traceback
        traceback.print_exc()


async def web_search_example():
    """Example using web search (requires Tavily API key)."""
    
    if not os.getenv("TAVILY_API_KEY"):
        print("Skipping web search example - TAVILY_API_KEY not found")
        return
    
    print("\n=== Web Search Example ===\n")
    
    # Create a web search source
    search_source = NewsSource(
        name="Carbon Market Search",
        source_type=SourceType.WEB_SEARCH,
        search_query="carbon market pricing news",
        search_domains=["reuters.com", "bloomberg.com"],
        max_age_hours=24,
        extract_instructions="""
        Search for recent carbon market news and extract relevant articles.
        
        Steps:
        1. Use web_search with the carbon market query
        2. Focus on results from reuters.com and bloomberg.com
        3. Extract content from promising URLs
        4. Filter for articles within 24 hours
        5. Ensure articles contain carbon market information
        """
    )
    
    config = CollectionConfig(
        sources=[search_source],
        max_articles_per_source=3,
        concurrent_sources=1
    )
    
    orchestrator = NewsCollectionOrchestrator()
    
    try:
        collection_run = await orchestrator.collect_news(config)
        raw_articles = orchestrator.get_all_articles(collection_run)
        
        print(f"Web search found {len(raw_articles)} articles:")
        for article in raw_articles:
            print(f"  - {article.title or 'No title'}")
            print(f"    {article.url}")
        
    except Exception as e:
        print(f"Web search error: {e}")


async def main():
    """Main function to run examples."""
    
    # Check for required environment variables
    if not os.getenv("OPENROUTER_API_KEY"):
        print("Error: OPENROUTER_API_KEY environment variable is required")
        print("Please set this in your .env file or environment")
        return
    
    # Run examples
    await simple_example()
    await web_search_example()
    
    print("\n=== Example Complete ===")
    print("\nTo use this system:")
    print("1. Configure your sources in config.yaml")
    print("2. Run: python main.py")
    print("3. Check collected_news.json for results")


if __name__ == "__main__":
    asyncio.run(main())
