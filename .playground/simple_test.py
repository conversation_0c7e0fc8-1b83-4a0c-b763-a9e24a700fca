#!/usr/bin/env python3
"""
Simple test of the AI News Collector system.

This script demonstrates basic news collection from configured sources
without the full extraction pipeline.

Usage:
    python simple_test.py

Requirements:
    - OPENROUTER_API_KEY environment variable set
    - TAVILY_API_KEY environment variable set
"""

import os
import sys
import asyncio
import yaml
from datetime import datetime

# Add current directory to sys.path
sys.path.append(os.path.dirname(__file__))

from ai_news_collector.models import NewsCollectionConfig
from ai_news_collector.agents import create_news_collector


async def main():
    """Simple news collection test."""
    print("🔧 AI News Collector - Simple Test")
    print("=" * 40)
    
    # Check environment variables
    required_vars = ['OPENROUTER_API_KEY', 'TAVILY_API_KEY']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"❌ Missing environment variables: {', '.join(missing_vars)}")
        print("\nPlease set these environment variables:")
        for var in missing_vars:
            print(f"   export {var}=your_api_key_here")
        return
    
    print("✅ Environment variables configured")
    
    # Load configuration
    try:
        with open('ai_news_collector/config.yaml', 'r') as f:
            config_data = yaml.safe_load(f)
        config = NewsCollectionConfig(**config_data)
        print(f"✅ Loaded configuration with {len(config.sources)} sources")
        
        # Show enabled sources
        enabled_sources = [s for s in config.sources if s.enabled]
        print(f"🔍 Enabled sources: {len(enabled_sources)}")
        for source in enabled_sources[:3]:  # Show first 3
            print(f"   - {source.name} ({source.extraction_mode.value})")
        if len(enabled_sources) > 3:
            print(f"   ... and {len(enabled_sources) - 3} more")
        
    except Exception as e:
        print(f"❌ Error loading configuration: {e}")
        return
    
    # Create news collector
    try:
        collector = create_news_collector()
        print("✅ Created news collector")
    except Exception as e:
        print(f"❌ Error creating collector: {e}")
        return
    
    print(f"\n🔍 Starting news collection (may take a few minutes)...")
    print(f"⏰ Started at: {datetime.now().strftime('%H:%M:%S')}")
    print("-" * 40)
    
    # Collect news from all sources
    try:
        result = await collector.collect_news(config)
        
        print(f"\n🎉 Collection completed!")
        print(f"📰 Total articles found: {len(result.all_articles)}")
        print(f"✅ Successful sources: {result.successful_sources}")
        print(f"❌ Failed sources: {result.failed_sources}")
        
        # Show results by source
        print(f"\n📊 Results by source:")
        for extraction_result in result.results:
            status = "✅" if extraction_result.success else "❌"
            article_count = len(extraction_result.articles)
            print(f"   {status} {extraction_result.source_id}: {article_count} articles")
            
            if not extraction_result.success:
                print(f"      Error: {extraction_result.error_message}")
        
        # Show sample articles
        if result.all_articles:
            print(f"\n📄 Sample articles:")
            for i, article in enumerate(result.all_articles[:5], 1):
                print(f"\n{i}. {article.title}")
                print(f"   Source ID: {article.source_id}")
                print(f"   Outlet: {article.outlet or 'Unknown'}")
                print(f"   URL: {article.url}")
                print(f"   Published: {article.published_at or 'Unknown'}")
                print(f"   Content length: {len(article.content)} chars")
        
        print(f"\n⏰ Completed at: {datetime.now().strftime('%H:%M:%S')}")
        
    except Exception as e:
        print(f"❌ Error during collection: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()
